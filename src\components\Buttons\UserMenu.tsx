'use client';

import {
    IconChevronRight,
    IconDots,
    IconHeart,
    IconLogout,
    IconMessage,
    IconPlayerPause,
    IconSettings,
    IconStar,
    IconSwitchHorizontal,
    IconTrash,
    IconShield,
    IconDatabase,
  } from '@tabler/icons-react';
  import { ActionIcon, Avatar, Group, Menu, Text, useMantineTheme } from '@mantine/core';
  import { getUserValue } from '../../lib/common';
  import { useSession } from 'next-auth/react';
  import { signOut } from "next-auth/react";
  import { deleteUserValue } from "../../lib/common";
  import { useEffect, useState } from "react";
  import { ADMIN_EMAIL } from '../../lib/config';
  import { notifications } from '@mantine/notifications';
  import { PointsDisplay } from '../Points/PointsDisplay';

  export function UserMenu() {
    const theme = useMantineTheme();
    const { data: session } = useSession();
    const email = session?.user?.email || '';
    const [avatarUrl, setAvatarUrl] = useState(getUserValue(email, 'avatar_url'));
    const [fullName, setFullName] = useState(getUserValue(email, 'full_name'));
    const [backupLoading, setBackupLoading] = useState(false);

    useEffect(() => {
      const handleStorageChange = () => {
        const newAvatarUrl = getUserValue(email, 'avatar_url');
        const newFullName = getUserValue(email, 'full_name');
        if (newAvatarUrl !== avatarUrl) {
          setAvatarUrl(newAvatarUrl);
        }
        if (newFullName !== fullName) {
          setFullName(newFullName);
        }
      };

      window.addEventListener('storage', handleStorageChange);
      window.addEventListener('userDataUpdated', handleStorageChange);

      return () => {
        window.removeEventListener('storage', handleStorageChange);
        window.removeEventListener('userDataUpdated', handleStorageChange);
      };
    }, [email, avatarUrl, fullName]);

    const handleBackup = async () => {
      setBackupLoading(true);
      try {
        const response = await fetch('/api/backup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const result = await response.json();

        if (response.status === 429) {
          // Too many requests - backup interval not met
          notifications.show({
            title: 'Backup Too Soon',
            message: result.message || 'Please wait before creating another backup.',
            color: 'orange',
          });
        } else if (result.success) {
          notifications.show({
            title: 'Backup Successful',
            message: `Backup completed successfully. ${result.files?.length || 0} files backed up.`,
            color: 'green',
          });
        } else {
          notifications.show({
            title: 'Backup Failed',
            message: result.message || 'Backup failed. Please try again.',
            color: 'red',
          });
        }
      } catch (error) {
        console.error('Backup error:', error);
        notifications.show({
          title: 'Backup Error',
          message: 'An error occurred during backup. Please try again.',
          color: 'red',
        });
      } finally {
        setBackupLoading(false);
      }
    };

    return (
      <Group justify="center">
        <Menu
          withArrow
          width={300}
          position="bottom"
          transitionProps={{ transition: 'pop' }}
          withinPortal
        >
          <Menu.Target>
            <ActionIcon variant="default">
              <IconDots size={16} stroke={1.5} />
            </ActionIcon>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item rightSection={<IconChevronRight size={16} stroke={1.5} />}>
              <Group onClick={() => window.location.href = '/'}>
                <Avatar
                  radius="xl"
                  src={avatarUrl}
                />

                <div>
                  <Text fw={500}>{fullName}</Text>
                  <Text size="xs" c="dimmed">
                    {email}
                  </Text>
                  <PointsDisplay variant="text" size="xs" showRefresh={true} />
                </div>
              </Group>
            </Menu.Item>

            <Menu.Divider />

            <Menu.Item
              leftSection={<IconHeart size={16} stroke={1.5} color={theme.colors.blue[6]} />}
              onClick={() => window.location.href = '/bookmark'}
            >
              Bookmarks
            </Menu.Item>

            {email === ADMIN_EMAIL && (
              <Menu.Item
                leftSection={<IconShield size={16} stroke={1.5} color={theme.colors.red[6]} />}
                onClick={() => window.location.href = '/admin'}
              >
                Admin Dashboard
              </Menu.Item>
            )}

            <Menu.Label>Tools</Menu.Label>

            <Menu.Item
              leftSection={<IconDatabase size={16} stroke={1.5} color={theme.colors.green[6]} />}
              onClick={handleBackup}
              disabled={backupLoading}
            >
              {backupLoading ? 'Creating Backup...' : 'Backup Data'}
            </Menu.Item>

            <Menu.Item color="red"  leftSection={<IconLogout size={16} stroke={1.5} />} onClick={() => {
              deleteUserValue(email, "avatar_url");
              deleteUserValue(email, "full_name");
              signOut();
            }}>Logout</Menu.Item>

          </Menu.Dropdown>
        </Menu>
      </Group>
    );
  }
