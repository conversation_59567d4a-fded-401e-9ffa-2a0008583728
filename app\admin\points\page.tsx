'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import {
  Container,
  Title,
  Card,
  Table,
  Text,
  Badge,
  Group,
  Select,
  TextInput,
  Button,
  Stack,
  Alert,
  Pagination,
  LoadingOverlay,
  Divider,
  ActionIcon,
  Tooltip,
  ScrollArea
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { useDebouncedValue } from '@mantine/hooks';
import { 
  IconCoins, 
  IconSearch, 
  IconRefresh, 
  IconArrowUp, 
  IconArrowDown,
  IconArrowsExchange,
  IconGift,
  IconShoppingCart,
  IconUserPlus,
  IconFilter
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { getAllTransactionHistory, type TransactionLog } from '../../../src/lib/points';
import { useAuthRedirect } from '../../../src/hooks/useAuthRedirect';
import FullLayout from '../../../src/components/layouts/FullLayout';
import { ADMIN_EMAIL } from '../../../src/lib/config';

const ITEMS_PER_PAGE = 20;

const getTransactionIcon = (type: string) => {
  switch (type) {
    case 'SIGNUP':
      return <IconUserPlus size={16} color="green" />;
    case 'CREATE_CONTACT':
      return <IconShoppingCart size={16} color="red" />;
    case 'TRANSFER_SEND':
      return <IconArrowUp size={16} color="red" />;
    case 'TRANSFER_RECEIVE':
      return <IconArrowDown size={16} color="green" />;
    case 'TRANSFER_ROLLBACK':
      return <IconRefresh size={16} color="gray" />;
    case 'ADMIN_LOAD':
      return <IconGift size={16} color="blue" />;
    case 'ADMIN_UNLOAD':
      return <IconArrowsExchange size={16} color="orange" />;
    default:
      return <IconCoins size={16} />;
  }
};

const getTransactionColor = (type: string, pointsChange: number) => {
  if (pointsChange > 0) return 'green';
  if (pointsChange < 0) return 'red';
  return 'gray';
};

const formatTransactionType = (type: string) => {
  switch (type) {
    case 'SIGNUP':
      return 'Signup Bonus';
    case 'CREATE_CONTACT':
      return 'Contact Creation';
    case 'TRANSFER_SEND':
      return 'Points Sent';
    case 'TRANSFER_RECEIVE':
      return 'Points Received';
    case 'TRANSFER_ROLLBACK':
      return 'Transfer Rollback';
    case 'ADMIN_LOAD':
      return 'Admin Load';
    case 'ADMIN_UNLOAD':
      return 'Admin Unload';
    default:
      return type;
  }
};

export default function AdminPointsPage() {
  const { data: session, status } = useSession();
  const { isLoading } = useAuthRedirect();
  
  const [transactions, setTransactions] = useState<TransactionLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  
  // Filters
  const [emailFilter, setEmailFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [dateFrom, setDateFrom] = useState<Date | null>(null);
  const [dateTo, setDateTo] = useState<Date | null>(null);
  
  const [debouncedSearch] = useDebouncedValue(searchQuery, 300);
  const [debouncedEmail] = useDebouncedValue(emailFilter, 300);

  // Check if user is admin
  const isAdmin = session?.user?.email === ADMIN_EMAIL;

  const fetchTransactions = useCallback(async (page: number = 1) => {
    if (!isAdmin) return;
    
    setLoading(true);
    try {
      const filters = {
        email: debouncedEmail || undefined,
        transactionType: typeFilter || undefined,
        dateFrom: dateFrom?.toISOString() || undefined,
        dateTo: dateTo?.toISOString() || undefined,
        searchQuery: debouncedSearch || undefined,
      };

      const result = await getAllTransactionHistory(page, ITEMS_PER_PAGE, filters);
      
      setTransactions(result.data);
      setTotalPages(result.totalPages);
      setTotal(result.total);
      
    } catch (error) {
      console.error('Error fetching transactions:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to load transaction history',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  }, [isAdmin, typeFilter, debouncedSearch, debouncedEmail, dateFrom, dateTo]);

  useEffect(() => {
    if (isAdmin) {
      setCurrentPage(1);
      fetchTransactions(1);
    }
  }, [fetchTransactions]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchTransactions(page);
  };

  const clearFilters = () => {
    setEmailFilter('');
    setTypeFilter('');
    setSearchQuery('');
    setDateFrom(null);
    setDateTo(null);
    setCurrentPage(1);
  };

  if (isLoading || status === 'loading') {
    return (
      <FullLayout>
        <Container size="xl" py="xl">
          <LoadingOverlay visible />
        </Container>
      </FullLayout>
    );
  }

  if (!session) {
    return null;
  }

  if (!isAdmin) {
    return (
      <FullLayout>
        <Container size="xl" py="xl">
          <Alert color="red" title="Access Denied">
            You do not have permission to access this page.
          </Alert>
        </Container>
      </FullLayout>
    );
  }

  const transactionTypeOptions = [
    { value: '', label: 'All Types' },
    { value: 'SIGNUP', label: 'Signup Bonus' },
    { value: 'CREATE_CONTACT', label: 'Contact Creation' },
    { value: 'TRANSFER_SEND', label: 'Points Sent' },
    { value: 'TRANSFER_RECEIVE', label: 'Points Received' },
    { value: 'TRANSFER_ROLLBACK', label: 'Transfer Rollback' },
    { value: 'ADMIN_LOAD', label: 'Admin Load' },
    { value: 'ADMIN_UNLOAD', label: 'Admin Unload' },
  ];

  return (
    <FullLayout>
      <Container size="xl" py="xl">
        <Stack gap="lg">
          <Group justify="space-between" align="center">
            <Title order={2}>
              <Group gap="sm">
                <IconCoins size={28} />
                Admin Points Management
              </Group>
            </Title>
          </Group>

          {/* Filters */}
          <Card withBorder>
            <Stack gap="md">
              <Group justify="space-between" align="center">
                <Text fw={500} size="sm">Filters</Text>
                <Button
                  variant="light"
                  size="xs"
                  leftSection={<IconRefresh size={14} />}
                  onClick={clearFilters}
                >
                  Clear All
                </Button>
              </Group>
              
              <Group grow>
                <TextInput
                  label="User Email"
                  placeholder="Filter by user email"
                  value={emailFilter}
                  onChange={(event) => setEmailFilter(event.currentTarget.value)}
                  leftSection={<IconSearch size={16} />}
                />
                
                <Select
                  label="Transaction Type"
                  placeholder="All types"
                  data={transactionTypeOptions}
                  value={typeFilter}
                  onChange={(value) => setTypeFilter(value || '')}
                  clearable
                />
              </Group>
              
              <Group grow>
                <TextInput
                  label="Search"
                  placeholder="Search in description or reference"
                  value={searchQuery}
                  onChange={(event) => setSearchQuery(event.currentTarget.value)}
                  leftSection={<IconSearch size={16} />}
                />
                
                <Group grow>
                  <DatePickerInput
                    label="From Date"
                    placeholder="Select start date"
                    value={dateFrom}
                    onChange={(dateString: string | null) => setDateFrom(dateString ? (new Date(dateString)) : null)}
                    clearable
                  />
                  <DatePickerInput
                    label="To Date"
                    placeholder="Select end date"
                    value={dateTo}
                    onChange={(dateString: string | null) => setDateTo(dateString ? (new Date(dateString)) : null)}
                    clearable
                  />
                </Group>
              </Group>
            </Stack>
          </Card>

          {/* Results Summary */}
          <Group justify="space-between" align="center">
            <Text size="sm" c="dimmed">
              Showing {transactions.length} of {total} transactions
            </Text>
            <Badge variant="light" color="blue">
              Page {currentPage} of {totalPages}
            </Badge>
          </Group>

          {/* Transaction History Table */}
          <Card withBorder>
            <ScrollArea>
              <Table striped highlightOnHover>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Date</Table.Th>
                    <Table.Th>User Email</Table.Th>
                    <Table.Th>Type</Table.Th>
                    <Table.Th>Points</Table.Th>
                    <Table.Th>Before</Table.Th>
                    <Table.Th>After</Table.Th>
                    <Table.Th>Description</Table.Th>
                    <Table.Th>Transfer Details</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {loading ? (
                    <Table.Tr>
                      <Table.Td colSpan={8}>
                        <LoadingOverlay visible />
                      </Table.Td>
                    </Table.Tr>
                  ) : transactions.length === 0 ? (
                    <Table.Tr>
                      <Table.Td colSpan={8}>
                        <Text ta="center" c="dimmed" py="xl">
                          No transactions found
                        </Text>
                      </Table.Td>
                    </Table.Tr>
                  ) : (
                    transactions.map((transaction) => (
                      <Table.Tr key={transaction.id}>
                        <Table.Td>
                          <Text size="sm">
                            {new Date(transaction.created_at).toLocaleString()}
                          </Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size="sm" fw={500}>
                            {transaction.email}
                          </Text>
                        </Table.Td>
                        <Table.Td>
                          <Group gap="xs">
                            {getTransactionIcon(transaction.transaction_type)}
                            <Text size="sm">
                              {formatTransactionType(transaction.transaction_type)}
                            </Text>
                          </Group>
                        </Table.Td>
                        <Table.Td>
                          <Badge
                            color={getTransactionColor(transaction.transaction_type, transaction.points_change)}
                            variant="light"
                          >
                            {transaction.points_change > 0 ? '+' : ''}{transaction.points_change}
                          </Badge>
                        </Table.Td>
                        <Table.Td>
                          <Text size="sm">{transaction.points_before}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size="sm">{transaction.points_after}</Text>
                        </Table.Td>
                        <Table.Td>
                          <Text size="sm" lineClamp={2}>
                            {transaction.description || '-'}
                          </Text>
                        </Table.Td>
                        <Table.Td>
                          {(transaction.from_email || transaction.to_email) ? (
                            <Stack gap={2}>
                              {transaction.from_email && (
                                <Text size="xs" c="dimmed">
                                  From: {transaction.from_email}
                                </Text>
                              )}
                              {transaction.to_email && (
                                <Text size="xs" c="dimmed">
                                  To: {transaction.to_email}
                                </Text>
                              )}
                            </Stack>
                          ) : (
                            <Text size="sm" c="dimmed">-</Text>
                          )}
                        </Table.Td>
                      </Table.Tr>
                    ))
                  )}
                </Table.Tbody>
              </Table>
            </ScrollArea>
          </Card>

          {/* Pagination */}
          {totalPages > 1 && (
            <Group justify="center">
              <Pagination
                value={currentPage}
                onChange={handlePageChange}
                total={totalPages}
                size="sm"
              />
            </Group>
          )}
        </Stack>
      </Container>
    </FullLayout>
  );
}
