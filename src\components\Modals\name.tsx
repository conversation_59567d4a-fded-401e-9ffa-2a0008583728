"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Modal, TextInput, Button, Group, Text, Box, Loader, Select } from '@mantine/core';
import { fetchData, updateData, type SupabaseResponse } from '../../lib/supabase';
import { isValidODudeName, fetchDomain, isIOSDevice, resetIOSZoom } from '../../lib/common';
import { NAME_SLOTS } from '../../lib/config';
import { getUserContactLimit } from '../../lib/user-settings';
import { insertContactData, prepareContactData } from '../Search';
import { notifications } from '@mantine/notifications';

// Domain options for the select dropdown - generated from NAME_SLOTS
const domainOptions = NAME_SLOTS.map((slot, index) => ({
  value: slot,
  label: `@${slot}`,
  default: index === 0 // First item is default
}));

interface NameModalProps {
  opened: boolean;
  onClose: () => void;
  existingName?: string; // Optional parameter for updating existing contact
  onContactSaved?: (newName?: string) => void; // Callback to refresh contacts list, with optional new name for updates
}

export function NameModal({ opened, onClose, existingName, onContactSaved }: NameModalProps) {
  const { data: session } = useSession();
  const [username, setUsername] = useState('');
  const [tld, setTld] = useState('me');
  const [profileName, setProfileName] = useState('');
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(false);
  const [nameExists, setNameExists] = useState(false);
  const [nameValid, setNameValid] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isUpdateMode, setIsUpdateMode] = useState(false);
  const [originalName, setOriginalName] = useState('');

  const fullName = username && tld ? `${username}@${tld}`.toLowerCase() : '';

  // Custom close handler that resets iOS zoom
  const handleClose = () => {
    resetIOSZoom();
    onClose();
  };

  // Handle cancel button click with zoom reset
  const handleCancel = () => {
    resetIOSZoom();
    onClose();
  };

  useEffect(() => {
    // Reset states when modal opens
    if (opened) {
      if (existingName) {
        // Update mode - fetch existing data
        setIsUpdateMode(true);
        setOriginalName(existingName);
        fetchExistingContact(existingName);
      } else {
        // New contact mode
        setIsUpdateMode(false);
        setOriginalName('');
        setUsername('');
        setTld('me');
        setProfileName('');
      }
      setNameExists(false);
      setNameValid(true);
      setSaveSuccess(false);
    }
  }, [opened, existingName]);

  const fetchExistingContact = async (name: string) => {
    if (!session?.user?.email) return;

    setLoading(true);
    try {
      const { data, error } = await fetchData('contact', {
        select: 'name, profile',
        filter: [
          { column: 'name', value: name.toLowerCase() },
          { column: 'profile_email', value: session.user.email }
        ],
      });

      if (!error && data && Array.isArray(data) && data.length > 0) {
        const contact = data[0];
        const nameParts = contact.name.split('@');
        if (nameParts.length === 2) {
          setUsername(nameParts[0]);
          setTld(nameParts[1]);
        }
        setProfileName(contact.profile || '');
      } else {
        // If no matching contact found or error occurred, reset to new contact mode
        setIsUpdateMode(false);
        setUsername('');
        setTld('me');
        setProfileName('');
      }
    } catch (error) {
      console.error('Error fetching contact:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const checkNameAvailability = async () => {
      if (!fullName || !isValidODudeName(fullName)) {
        setNameValid(false);
        setNameExists(false);
        return;
      }

      setNameValid(true);
      setChecking(true);

      try {
        // Skip availability check if we're in update mode and the name hasn't changed
        if (isUpdateMode && fullName === originalName) {
          setNameExists(false);
          return;
        }

        const { data, error } = await fetchData('contact', {
          select: 'name',
          filter: [{ column: 'name', value: fullName.toLowerCase() }],
        });

        if (!error && data) {
          setNameExists(Array.isArray(data) && data.length > 0);
        }
      } catch (error) {
        console.error('Error checking name availability:', error);
      } finally {
        setChecking(false);
      }
    };

    const debounceTimer = setTimeout(() => {
      if (fullName) {
        checkNameAvailability();
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [fullName, isUpdateMode, originalName]);

  const handleSave = async () => {
    if (!session?.user?.email || !fullName || (!isUpdateMode && nameExists) || !nameValid || !profileName) {
      return;
    }

    // Check contact limit for new contacts (not updates)
    if (!isUpdateMode) {
      try {
        const { data: existingContacts } = await fetchData('contact', {
          select: 'name',
          filter: [{ column: 'profile_email', value: session.user.email }],
        });

        const currentCount = Array.isArray(existingContacts) ? existingContacts.length : 0;
        const userContactLimit = await getUserContactLimit(session.user.email);

        if (currentCount >= userContactLimit) {
          notifications.show({
            title: 'Creation Limit Reached',
            message: `You can only create a maximum of ${userContactLimit} ODude Names. Please delete an existing name to create a new one.`,
            color: 'orange',
          });
          return;
        }
      } catch (error) {
        console.error('Error checking contact limit:', error);
        notifications.show({
          title: 'Error',
          message: 'Failed to check contact limit. Please try again.',
          color: 'red',
        });
        return;
      }
    }

    setLoading(true);

    try {
      const domainResult = await fetchDomain(fullName);

      if (domainResult && domainResult.error) {
        if (isUpdateMode) {
          const updateResult: SupabaseResponse<any> = await updateData('contact', {
            name: fullName.toLowerCase(),
            profile: profileName,
          }, {
            column: 'name',
            value: originalName.toLowerCase(),
          });

          if (updateResult && updateResult.error) throw updateResult.error;
        } else {
          const { data: existingData } = await fetchData('contact', {
            select: '*',
            filter: [{ column: 'profile_email', value: session.user.email }],
          });

          if (Array.isArray(existingData) && existingData.length > 0) {
            const insertResult = await insertContactData({
              name: fullName.toLowerCase(),
              profile: profileName,
             profile_email: session.user.email,
            }, session.user.email);

            if (insertResult && insertResult.error) {
              if ((insertResult.error as any).code === 'INSUFFICIENT_POINTS') {
                notifications.show({
                  title: 'Insufficient Points',
                  message: (insertResult.error as any).message,
                  color: 'red',
                });
                return;
              }
              throw insertResult.error;
            }
          } else {
            const insertResult = await insertContactData({
              name: fullName.toLowerCase(),
              profile: profileName,
             profile_email: session.user.email,
            }, session.user.email);

            if (insertResult && insertResult.error) {
              if ((insertResult.error as any).code === 'INSUFFICIENT_POINTS') {
                notifications.show({
                  title: 'Insufficient Points',
                  message: (insertResult.error as any).message,
                  color: 'red',
                });
                return;
              }
              throw insertResult.error;
            }
          }
        }
      } else {
        const existingRecord = await fetchData('contact', {
          filter: [{ column: 'name', value: domainResult.name.toLowerCase() }],
          single: true
        });

        if (!existingRecord.data) {
          const contact = prepareContactData(domainResult);
          // Ensure name is lowercase
          contact.name = contact.name.toLowerCase();
          // Add profile_email to the contact data
         // contact.profile_email = session.user.email;
          // Use profile name as fallback if not present in domain data
          if (!contact.profile) contact.profile = profileName;

          const insertResult = await insertContactData(contact, session.user.email);
          if (insertResult && insertResult.error) {
            if ((insertResult.error as any).code === 'INSUFFICIENT_POINTS') {
              notifications.show({
                title: 'Insufficient Points',
                message: (insertResult.error as any).message,
                color: 'red',
              });
              return;
            }
            console.error('Insert failed:', insertResult.error);
          } else {
            console.log('Record inserted successfully:', (insertResult as any).data);
          }
        } else {
          console.log('Record already exists:', existingRecord.data);
        }
      }

      setSaveSuccess(true);

      // Call the callback to refresh contacts list
      if (onContactSaved) {
        // Pass the new name if this was an update operation
        onContactSaved(isUpdateMode ? fullName.toLowerCase() : undefined);
      }

      setTimeout(() => {
        resetIOSZoom();
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Error saving profile:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={isUpdateMode ? "Update ODude Name" : "Set New ODude Name"}
      centered
    >
      <Box mb="md">
        <Text size="sm" mb="xs">Please enter your desired ODude Name</Text>
        <TextInput
          placeholder="name"
          label="ODude Name"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          onBlur={() => resetIOSZoom()}
          disabled={loading}
          required
          rightSection={
            <Select
              data={domainOptions}
              value={tld}
              onChange={(value) => setTld(value || 'me')}
              disabled={loading}
              rightSectionWidth={28}
              styles={{
                input: {
                  fontWeight: 500,
                  borderTopLeftRadius: 0,
                  borderBottomLeftRadius: 0,
                  width: 140,
                  marginRight: -2,
                  border: 'none',
                  backgroundColor: 'transparent',
                },
              }}
              comboboxProps={{ withinPortal: false }}
            />
          }
          rightSectionWidth={140}
          mb="xs"
        />

        {checking && (
          <Group gap="xs">
            <Loader size="xs" />
            <Text size="xs" c="dimmed">Checking availability...</Text>
          </Group>
        )}

        {!checking && fullName && !nameValid && (
          <Text size="xs" c="red">
            Please enter a valid name format
          </Text>
        )}

        {!checking && nameExists && (
          <Text size="xs" c="red">
            {fullName} is already taken. Please choose another.
          </Text>
        )}

        {!checking && fullName && !nameExists && nameValid && (
          <Text size="xs" c="green">
            {fullName} is available!
          </Text>
        )}
      </Box>

      <TextInput
        label="Public Profile Name"
        placeholder="Enter your profile name"
        value={profileName}
        onChange={(e) => setProfileName(e.target.value)}
        onBlur={() => resetIOSZoom()}
        disabled={loading}
        required
        mb="md"
      />

      <Group justify={isIOSDevice() ? "flex-start" : "flex-end"}>
          <Button
          onClick={handleSave}
          loading={loading}
          disabled={!fullName || (!isUpdateMode && nameExists) || !nameValid || !profileName || loading}
        >
          {saveSuccess ? 'Saved!' : (isUpdateMode ? 'Update' : 'Save')}
        </Button>
        <Button variant="outline" onClick={handleCancel} disabled={loading}>
          Cancel
        </Button>
      
      </Group>
    </Modal>
  );
}