'use client';

import { LoadingOverlay, Container } from '@mantine/core';
import { useAdminGuard } from 'src/hooks/useAdminGuard';
import { AdminPasskeyModal } from 'src/components/Admin/AdminPasskeyModal';
import FullLayout from './FullLayout';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const {
    isLoading,
    isAuthorized,
    needsPasskey,
    showPasskeyModal,
    setShowPasskeyModal,
    handlePasskeySuccess,
  } = useAdminGuard();

  if (isLoading) {
    return (
      <FullLayout>
        <Container size="xl" py="xl">
          <LoadingOverlay visible />
        </Container>
      </FullLayout>
    );
  }

  if (needsPasskey) {
    return (
      <FullLayout>
        <Container size="xl" py="xl">
          <LoadingOverlay visible />
          <AdminPasskeyModal
            opened={showPasskeyModal}
            onClose={() => setShowPasskeyModal(false)}
            onSuccess={handlePasskeySuccess}
          />
        </Container>
      </FullLayout>
    );
  }

  if (!isAuthorized) {
    return (
      <FullLayout>
        <Container size="xl" py="xl">
          <LoadingOverlay visible />
        </Container>
      </FullLayout>
    );
  }

  return <FullLayout>{children}</FullLayout>;
}
