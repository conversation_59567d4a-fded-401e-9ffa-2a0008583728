import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';
import { MantineProvider } from 'src/providers/MantineProvider';
import type { Metadata } from 'next';
import React from 'react';
import { SessionProvider } from "next-auth/react";
import { auth } from "auth";
import { FooterProvider } from "src/providers/FooterProvider";
import AdsScriptLoader from 'src/components/AdSense/AdsScriptLoader';
import { AdminSessionInitializer } from 'src/components/Admin/AdminSessionInitializer';

export const metadata: Metadata = {
  title: 'ODude Desktop',
  description: 'Next.js app using Mantine AppShell',
};

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  return (
    <html lang="en">
      <head>
        {/* Removed AdSense <Script> as per best practice */}
      </head>
      <body>
        <AdsScriptLoader />
        <SessionProvider session={session}>
          <MantineProvider>
            <FooterProvider>
              <AdminSessionInitializer />
              {children}
            </FooterProvider>
          </MantineProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
