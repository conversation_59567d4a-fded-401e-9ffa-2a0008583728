// app/profile/[id]/layout.tsx
// Dynamic metadata for profile pages

import type { Metadata } from 'next';
import { ReactNode } from 'react';
import { getSupabaseClient } from 'src/lib/supabase';

export async function generateMetadata(
  { params }: { params: Promise<{ id: string }> }
): Promise<Metadata> {
  const { id } = await params;
  const contactName = decodeURIComponent(id).toLowerCase();

  try {
    const client = getSupabaseClient();
    const { data } = await client
      .from('contact')
      .select('profile,name,description')
      .eq('name', contactName)
      .single();

    return {
      title: data?.profile ? `${data.profile} - ODude Profile` : `ODude Profile - ${contactName}`,
      description: data?.description || `Explore ${contactName}'s public profile on ODude.`,
    };
  } catch {
    return {
      title: `ODude Profile - ${contactName}`,
      description: `View user profile and links.`,
    };
  }
}

// DO NOT include `params` here
export default function Layout({ children }: { children: ReactNode }) {
  return <>{children}</>;
}
