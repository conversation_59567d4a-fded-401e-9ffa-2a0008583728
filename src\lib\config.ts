/**
 * Application Configuration Constants
 * 
 * This file contains all application-wide constants and configuration values.
 * Import these constants instead of hardcoding values throughout the application.
 */

// Contact Management Constants
export const MAX_CONTACTS_LIMIT = 4;

// Admin Configuration
export const ADMIN_EMAIL = "<EMAIL>";

// Image Configuration Constants
export const MAX_IMAGES_LIMIT = 4;
export const IMAGE_RESIZE_WIDTH = 500;

// Default URLs and Assets
export const DEFAULT_AVATAR_URL = "https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png";

// API Configuration
export const DOMAIN_API_BASE_URL = "https://api.odude.com/opensea.php";

// Notification Configuration
export const NOTIFICATION_POSITION = "bottom-right";
export const NOTIFICATION_SIZE = "sm";

// UI Configuration
export const SOCIAL_ICON_SIZE = 45;
export const SOCIAL_ICON_RADIUS = "xl";

// Validation Constants
export const MAX_ODUDE_NAME_LENGTH = 253;
export const MAX_ODUDE_NAME_SEGMENT_LENGTH = 63;

//View profile URL
export const VIEW_PROFILE_URL = "https://name.odude.com/profile/";

// Social Media Configuration
export const SOCIAL_SLOTS = ["twitter", "telegram", "youtube", "instagram", "facebook", "discord", "linkedin", "tiktok"];

// Cryptocurrency Configuration
export const CRYPTO_SLOTS = ["eth", "btc", "sol"];

// Notes Configuration
export const MAX_NOTES_CHARACTER_LIMIT = 100;

// Name Domain Configuration
export const NAME_SLOTS = ["me", "id", "info", "ai"];

// SMTP Configuration for Contact Us
export const SMTP_CONFIG = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: true, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || ''
  }
};

// Contact Us Configuration
export const CONTACT_EMAIL = process.env.CONTACT_EMAIL || '<EMAIL>';

// AdSense Configuration
export const ENABLE_ADSENSE = true;
export const ADSENSE_CLIENT_ID = 'ca-pub-9660854185566265';
//Google official values
//data-ad-client="ca-pub-3940256099942544"
//data-ad-slot="6300978111"


// FTP Configuration
export const FTP_HOST = "home.odude.com";
export const FTP_USER = "home";
export const FTP_PASSWORD = process.env.FTP_PASSWORD || "";
export const FTP_PATH = "/public_html/supabase_backup/";

// Backup Configuration
export const BACKUP_INTERVAL_HOURS = 24; // Minimum hours between backups

// Point System Configuration
export const SIGNUP_POINT = 2000;
export const CREATE_CONTACT_POINT = 1000;
export const BOOKMARK_POINT = 10;